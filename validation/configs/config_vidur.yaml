# Vidur 特有配置文件 - config_vidur.yaml
# 包含 vidur 模拟器特有的参数

# 集群配置
cluster_config:
  num_replicas: 1

# 副本配置
replica_config:
  memory_margin_fraction: 0.1
  num_pipeline_stages: 1
  network_device: "a100_pairwise_nvlink"

# 全局调度器配置
global_scheduler_config:
  type: "round_robin"

# 副本调度器配置 - 使用 vLLM 调度器，参数从 config_shared.yaml 获取
replica_scheduler_config:
  type: "vllm"
  # 注意：batch_size_cap, block_size, watermark_blocks_fraction, max_tokens_in_batch 
  # 都应该从 config_shared.yaml 中读取，通过脚本自动设置

# 请求生成器配置 - 使用 trace_replay 来重放完整的请求trace，限制请求数量进行测试
request_generator_config:
  type: "trace_replay"
  trace_file: "validation/results/vllm/vllm_results_small.csv"
  prefill_scale_factor: 1.0
  decode_scale_factor: 1.0
  time_scale_factor: 1.0  # 保持原始时间间隔
  # max_tokens: 从 config_shared.yaml 的 max_model_len 获取
  # 注意：TraceReplayRequestGenerator 会读取整个文件，可能需要在代码中限制请求数量

# 执行时间预测器配置 - 参考 README 示例优化参数以支持更大的 batch 和 token 数量
execution_time_predictor_config:
  type: "random_forrest"
  k_fold_cv_splits: 10
  no_cache: false
  kv_cache_prediction_granularity: 64
  prediction_max_prefill_chunk_size: 4096  # 保持4096，因为你的max_tokens是4096
  prediction_max_batch_size: 512  # 增加batch size预测范围
  prediction_max_tokens_per_request: 4096  # 匹配max_tokens
  attention_decode_batching_overhead_fraction: 0.1
  attention_prefill_batching_overhead_fraction: 0.1
  nccl_cpu_launch_overhead_ms: 0.02
  nccl_cpu_skew_overhead_per_device_ms: 0.0
  num_training_job_threads: -1
  skip_cpu_overhead_modeling: true
  
  # Random Forest 特有参数
  num_estimators: [250, 500, 750]
  max_depth: [8, 16, 32]
  min_samples_split: [2, 5, 10]

# 指标配置
metrics_config:
  write_metrics: true
  write_json_trace: false
  wandb_project: null
  wandb_group: null
  wandb_run_name: null
  enable_chrome_trace: true
  save_table_to_wandb: false
  store_plots: true
  store_operation_metrics: false
  store_token_completion_metrics: false
  store_request_metrics: true
  store_batch_metrics: true
  store_utilization_metrics: true
  keep_individual_batch_metrics: false
  subsamples: null
  min_batch_index: null
  max_batch_index: null
  output_dir: "validation/results/vidur"
  cache_dir: "cache"

# 时间限制（秒，0表示无限制）
time_limit: 0
